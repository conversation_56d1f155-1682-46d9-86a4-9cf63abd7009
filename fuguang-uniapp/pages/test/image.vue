<template>
  <view class="test-container">
    <view class="test-section">
      <text class="section-title">图片URL测试</text>
      
      <!-- 测试不同格式的图片路径 -->
      <view class="test-item">
        <text class="test-label">相对路径图片:</text>
        <text class="test-value">{{ relativePath }}</text>
        <text class="test-result">处理后: {{ getImageUrl(relativePath) }}</text>
        <image class="test-image" :src="getImageUrl(relativePath)" mode="aspectFill"></image>
      </view>
      
      <view class="test-item">
        <text class="test-label">完整URL图片:</text>
        <text class="test-value">{{ fullUrl }}</text>
        <text class="test-result">处理后: {{ getImageUrl(fullUrl) }}</text>
        <image class="test-image" :src="getImageUrl(fullUrl)" mode="aspectFill"></image>
      </view>
      
      <view class="test-item">
        <text class="test-label">空值测试:</text>
        <text class="test-value">{{ emptyValue }}</text>
        <text class="test-result">处理后: {{ getImageUrl(emptyValue) }}</text>
        <image class="test-image" :src="getImageUrl(emptyValue) || '/static/default-avatar.png'" mode="aspectFill"></image>
      </view>
    </view>
    
    <view class="test-section">
      <text class="section-title">头像上传测试</text>
      <u-button type="primary" @click="testUpload">测试头像上传</u-button>
      <view class="upload-result" v-if="uploadResult">
        <text class="test-label">上传结果:</text>
        <text class="test-value">{{ uploadResult }}</text>
        <image class="test-image" :src="uploadResult" mode="aspectFill"></image>
      </view>
    </view>
  </view>
</template>

<script>
import { getImageUrl } from '@/utils/request'
import { uploadAvatar } from '@/api/auth'
import { chooseImage } from '@/utils/common'

export default {
  data() {
    return {
      relativePath: '/profile/avatar/2025/01/18/test.jpg',
      fullUrl: 'http://localhost:8888/profile/avatar/2025/01/18/test.jpg',
      emptyValue: '',
      uploadResult: ''
    }
  },
  
  methods: {
    // 获取图片完整URL
    getImageUrl,
    
    async testUpload() {
      try {
        const images = await chooseImage(1)
        if (images.length > 0) {
          uni.showLoading({ title: '上传中...' })
          
          const res = await uploadAvatar(images[0])
          this.uploadResult = res.data.imgUrl
          
          uni.hideLoading()
          uni.showToast({
            title: '上传成功',
            icon: 'success'
          })
        }
      } catch (error) {
        uni.hideLoading()
        console.error('上传失败:', error)
        uni.showToast({
          title: '上传失败',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style scoped>
.test-container {
  padding: 40rpx;
}

.test-section {
  margin-bottom: 60rpx;
  padding: 40rpx;
  background: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 40rpx;
  display: block;
}

.test-item {
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.test-label {
  font-size: 28rpx;
  font-weight: bold;
  color: #666666;
  display: block;
  margin-bottom: 10rpx;
}

.test-value {
  font-size: 24rpx;
  color: #999999;
  display: block;
  margin-bottom: 10rpx;
  word-break: break-all;
}

.test-result {
  font-size: 24rpx;
  color: #007aff;
  display: block;
  margin-bottom: 20rpx;
  word-break: break-all;
}

.test-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 16rpx;
  border: 2rpx solid #e0e0e0;
}

.upload-result {
  margin-top: 40rpx;
  padding: 30rpx;
  background: #f0f9ff;
  border-radius: 16rpx;
}
</style>
