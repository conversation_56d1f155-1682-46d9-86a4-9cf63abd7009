<template>
  <view class="settings-container">
    <view class="settings-list">
      <view class="settings-group">
        <view class="settings-item" @click="goAbout">
          <view class="item-left">
            <u-icon name="info-circle" size="20" color="#666"></u-icon>
            <text class="item-text">关于我们</text>
          </view>
          <u-icon name="arrow-right" size="16" color="#999"></u-icon>
        </view>
        
        <view class="settings-item" @click="goPrivacy">
          <view class="item-left">
            <u-icon name="shield" size="20" color="#666"></u-icon>
            <text class="item-text">隐私政策</text>
          </view>
          <u-icon name="arrow-right" size="16" color="#999"></u-icon>
        </view>
        
        <view class="settings-item" @click="goUserAgreement">
          <view class="item-left">
            <u-icon name="file-text" size="20" color="#666"></u-icon>
            <text class="item-text">用户协议</text>
          </view>
          <u-icon name="arrow-right" size="16" color="#999"></u-icon>
        </view>
      </view>
      
      <view class="settings-group" v-if="isLoggedIn">
        <view class="settings-item logout-item" @click="logout">
          <view class="item-left">
            <u-icon name="logout" size="20" color="#ff4757"></u-icon>
            <text class="item-text logout-text">退出登录</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  computed: {
    isLoggedIn() {
      return !!uni.getStorageSync('token')
    }
  },
  
  methods: {
    goAbout() {
      uni.navigateTo({
        url: '/pages/about/index'
      })
    },
    
    goPrivacy() {
      uni.navigateTo({
        url: '/pages/agreement/privacy'
      })
    },
    
    goUserAgreement() {
      uni.navigateTo({
        url: '/pages/agreement/user'
      })
    },
    
    logout() {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            uni.removeStorageSync('token')
            uni.removeStorageSync('userInfo')
            
            uni.showToast({
              title: '已退出登录',
              icon: 'success'
            })
            
            // 返回上一页并刷新
            setTimeout(() => {
              uni.navigateBack()
            }, 1500)
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.settings-container {
  min-height: 100vh;
  background: #f8f8f8;
  padding: 20rpx;
}

.settings-group {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background: #f8f9fa;
  }
  
  .item-left {
    display: flex;
    align-items: center;
    
    .item-text {
      margin-left: 20rpx;
      font-size: 30rpx;
      color: #333;
    }
  }
  
  &.logout-item {
    .logout-text {
      color: #ff4757;
    }
  }
}
</style>
